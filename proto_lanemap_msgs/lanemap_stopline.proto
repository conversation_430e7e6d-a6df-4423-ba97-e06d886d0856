syntax = "proto2";

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";

message Stopline {
  optional uint32 id = 1;

  optional Linesegment line = 2;

  enum Type {
    NORMAL = 0;
    WAITTING = 1;
  };
  optional Type type = 3;

  enum Visibility {
    ENTITY = 0;
    VIRTUAL = 1;
  };
  optional Visibility visibility = 4;

  optional bool topo_filtered = 5;
  repeated uint32 lane_id = 6;
  repeated uint32 road_id = 7;
}
