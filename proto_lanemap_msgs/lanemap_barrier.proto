syntax = "proto2";

package robosense.rs_map.lanemap;

import "modules/rs_common_msgs/proto_lanemap_msgs/lanemap_geometry.proto";

message ParkingBarrier {
  enum BarrierStatus {
    INVALID = 0;
    OPEN = 1;
    CLOSE = 2;
  }

  enum BarrierType {
    BARRIER_UNKNOWN = 0;
    STRAIGHT_ARM_BARRIER = 1;  // 直杆
    FOLDING_ARM_BARRIER = 2; // 折杆
    FENCE_ARM_BARRIER = 3; // 栅栏
    BOLLARDS = 4; // 升降柱
  }

  optional uint32 id = 1;
  optional Linesegment polyline = 2;
  optional BarrierStatus status = 3;
  optional BarrierType type = 4;
  optional uint32 ref_lane_id = 5;            // 关联 lane 的uid
  optional uint32 ref_road_id = 6;            // 关联 road 的uid
  optional bool topo_filtered = 7;
}
