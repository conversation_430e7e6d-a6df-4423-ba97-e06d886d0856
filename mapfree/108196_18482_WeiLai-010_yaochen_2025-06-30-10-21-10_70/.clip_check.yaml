check:
  - record_file_name: bev_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: bev_object_ruby.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: bev_post_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: bev_post_object_ruby.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: bev_segment.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: blind_spot_map.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: camera_bm_fisheye.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_bm_pinhole.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: camera
    op_type: 2
  - record_file_name: camera_fm_fisheye.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_fm_narrow.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_fm_wide.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_lb_pinhole.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_lf_pinhole.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_lm_fisheye.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_rb_pinhole.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_rf_pinhole.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: camera_rm_fisheye.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: camera
    op_type: 0
  - record_file_name: control.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: control
    op_type: 0
  - record_file_name: faultreporter.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: ""
    op_type: 0
  - record_file_name: frequence.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: ""
    op_type: 0
  - record_file_name: ground_map.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: info.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: ""
    op_type: 0
  - record_file_name: lidar_bm_helios.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: lidar
    op_type: 2
  - record_file_name: lidar_semantic.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: lidar_semantic_occ.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: lidar_tm_em4_comp.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: lidar
    op_type: 2
  - record_file_name: lidar_tm_m1p.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: lidar
    op_type: 0
  - record_file_name: lidar_tm_mx.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: lidar
    op_type: 2
  - record_file_name: lidar_tm_ruby_plus.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: lidar
    op_type: 2
  - record_file_name: localization.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: localization
    op_type: 0
  - record_file_name: monitor.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: ""
    op_type: 0
  - record_file_name: navi_info.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: occupancy.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: planning.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: pnc
    op_type: 0
  - record_file_name: planning_traj.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: pnc
    op_type: 0
  - record_file_name: post_fusion_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: post_fusion_object_ruby.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: prediction.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: prediction
    op_type: 0
  - record_file_name: pure_perception.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_bm_fisheye_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_bm_pinhole_object.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: pv_fm_fisheye_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_fm_narrow_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_fm_narrow_object_small.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_fm_wide_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_fm_wide_object_small.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_lb_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_lb_pinhole_object_small.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_lf_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_lf_pinhole_object_small.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_lm_fisheye_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_post_bm_pinhole_object.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: pv_post_fm_narrow_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_post_fm_wide_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_post_lb_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_post_lf_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_post_rb_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_post_rf_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_rb_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_rb_pinhole_object_small.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_rf_pinhole_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_rf_pinhole_object_small.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: pv_rm_fisheye_object.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: radar_back_postprocess_object.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: radar_bm_conti.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: radar
    op_type: 2
  - record_file_name: radar_fm_conti.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: radar
    op_type: 2
  - record_file_name: radar_front_postprocess_object.record
    status: false
    info: Record Is Zero Message Count
    clip_rel_dir_name: perception
    op_type: 2
  - record_file_name: roadstructure.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: traffic_light.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: perception
    op_type: 0
  - record_file_name: version.record
    status: true
    info: Record Check Pass
    clip_rel_dir_name: ""
    op_type: 0