#!/usr/bin/env python3
"""
ROS Bag Topic and Message Type Analyzer
Analyzes all .bag files in the data2 directory structure and prints topics and message types.
Works without ROS installation by using pure Python bag reading libraries.
"""

import os
import sys
from collections import defaultdict

def find_bag_files(root_dir):
    """Find all .bag files in the directory structure."""
    bag_files = []
    for root, _, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.bag'):
                bag_files.append(os.path.join(root, file))
    return sorted(bag_files)

def read_bag_header(bag_file):
    """Read basic information from a bag file header."""
    try:
        with open(bag_file, 'rb') as f:
            # Read the bag file header
            header = f.read(13)
            if len(header) < 13:
                return None

            # Check if it's a valid bag file
            if header[:13] != b'#ROSBAG V2.0\n':
                return None

            # Try to read some basic info
            file_size = os.path.getsize(bag_file)
            return {
                'valid': True,
                'size': file_size,
                'version': '2.0'
            }
    except Exception as e:
        return {'valid': False, 'error': str(e)}

def analyze_bag_structure(bag_file):
    """Analyze bag file structure to extract topic information using rosbags library."""
    try:
        # Use rosbags library to read bag files
        from rosbags.rosbag1 import Reader

        topics = {}
        topic_counts = defaultdict(int)
        topic_types = {}

        with Reader(bag_file) as reader:
            # Get topic information from connections
            for connection in reader.connections:
                topic_name = connection.topic
                msg_type = connection.msgtype
                topic_types[topic_name] = msg_type
                topic_counts[topic_name] = 0

            # Count messages per topic
            for connection, _, _ in reader.messages():
                topic_counts[connection.topic] += 1

        # Build topics dictionary
        for topic_name, msg_type in topic_types.items():
            topics[topic_name] = {
                'type': msg_type,
                'count': str(topic_counts[topic_name])
            }

        return topics

    except ImportError as e:
        return {'error': f'rosbags library not available: {e}'}
    except Exception as e:
        # Fallback: basic file analysis
        header_info = read_bag_header(bag_file)
        if header_info and header_info.get('valid'):
            file_size_mb = header_info['size'] / (1024 * 1024)
            return {
                f"ROS_Bag_File_{os.path.basename(bag_file)}": {
                    'type': f"ROS Bag v{header_info.get('version', 'unknown')} - Analysis failed: {str(e)}",
                    'count': f"{file_size_mb:.1f} MB"
                }
            }
        else:
            return {'error': f'Could not analyze bag file: {str(e)}'}

def analyze_camera_naming():
    """Provide explanation of camera naming convention."""
    print("=" * 80)
    print("CAMERA NAMING CONVENTION ANALYSIS")
    print("=" * 80)
    print()
    print("Camera Position Codes:")
    print("  bm = Back Middle (rear center camera)")
    print("  fm = Front Middle (front center camera)")
    print("  lb = Left Back (left rear camera)")
    print("  lf = Left Front (left front camera)")
    print("  lm = Left Middle (left side camera)")
    print("  rb = Right Back (right rear camera)")
    print("  rf = Right Front (right front camera)")
    print("  rm = Right Middle (right side camera)")
    print()
    print("Camera Types:")
    print("  fisheye = Wide-angle fisheye lens cameras")
    print("  pinhole = Standard pinhole cameras")
    print("  narrow/wide = Different field-of-view configurations")
    print()
    print("This represents a typical 8-camera autonomous vehicle setup")
    print("providing 360-degree coverage around the vehicle.")
    print()

def main():
    """Main function to analyze all bag files."""
    print("ROS Bag Topic and Message Type Analyzer")
    print("=" * 80)

    # Analyze camera naming convention first
    analyze_camera_naming()

    # Find all bag files
    data_dir = "data2"
    if not os.path.exists(data_dir):
        print(f"Error: {data_dir} directory not found!")
        sys.exit(1)

    bag_files = find_bag_files(data_dir)

    if not bag_files:
        print(f"No .bag files found in {data_dir}")
        sys.exit(1)

    print(f"Found {len(bag_files)} bag files:")
    for bag_file in bag_files:
        print(f"  {bag_file}")
    print()

    # Analyze each bag file
    all_topics = defaultdict(set)

    for i, bag_file in enumerate(bag_files, 1):
        print(f"[{i}/{len(bag_files)}] Analyzing: {os.path.basename(bag_file)}")
        print("-" * 60)

        topics = analyze_bag_structure(bag_file)

        if not topics or 'error' in topics:
            error_msg = topics.get('error', 'Unknown error') if topics else 'Failed to analyze'
            print(f"  Failed to analyze this bag file: {error_msg}")
            print()
            continue

        print(f"  Topics ({len(topics)}):")
        for topic_name, topic_info in sorted(topics.items()):
            print(f"    {topic_name:<40} {topic_info['count']:<10} msgs : {topic_info['type']}")
            all_topics[topic_info['type']].add(topic_name)

        print()

    # Summary table of all topics and message types
    print("=" * 100)
    print("TOPIC TO MESSAGE TYPE TABLE")
    print("=" * 100)

    # Collect all unique topics and their types
    all_unique_topics = {}
    for msg_type, topics_set in all_topics.items():
        for topic in topics_set:
            all_unique_topics[topic] = msg_type

    if all_unique_topics:
        # Print table header
        print(f"{'TOPIC':<60} {'MESSAGE TYPE':<40}")
        print("-" * 100)

        # Print each topic and its message type
        for topic in sorted(all_unique_topics.keys()):
            msg_type = all_unique_topics[topic]
            print(f"{topic:<60} {msg_type:<40}")

        print("-" * 100)
        print(f"Total unique topics: {len(all_unique_topics)}")
        print(f"Total unique message types: {len(all_topics)}")
    else:
        print("No topics found in any bag files.")

    print("\n" + "=" * 100)
    print("MESSAGE TYPE SUMMARY")
    print("=" * 100)

    for msg_type in sorted(all_topics.keys()):
        topics_with_type = sorted(all_topics[msg_type])
        print(f"\n{msg_type} ({len(topics_with_type)} topics):")
        for topic in topics_with_type:
            print(f"  {topic}")

if __name__ == "__main__":
    main()