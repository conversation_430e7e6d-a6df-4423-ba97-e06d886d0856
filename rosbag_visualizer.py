#!/usr/bin/env python3
"""
ROS Bag Topic and Message Type Analyzer
Analyzes all .bag files in the data2 directory structure and prints topics and message types.
"""

import os
import subprocess
import sys
from pathlib import Path
from collections import defaultdict

def find_bag_files(root_dir):
    """Find all .bag files in the directory structure."""
    bag_files = []
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith('.bag'):
                bag_files.append(os.path.join(root, file))
    return sorted(bag_files)

def get_bag_info(bag_file):
    """Get topic information from a bag file using rosbag info."""
    try:
        # Run rosbag info command
        result = subprocess.run(['rosbag', 'info', bag_file],
                              capture_output=True, text=True, timeout=30)

        if result.returncode != 0:
            print(f"Error analyzing {bag_file}: {result.stderr}")
            return None

        return result.stdout
    except subprocess.TimeoutExpired:
        print(f"Timeout analyzing {bag_file}")
        return None
    except FileNotFoundError:
        print("Error: rosbag command not found. Please ensure ROS is installed and sourced.")
        return None
    except Exception as e:
        print(f"Error analyzing {bag_file}: {e}")
        return None

def parse_topics_from_info(info_output):
    """Parse topic information from rosbag info output."""
    topics = {}
    lines = info_output.split('\n')

    in_topics_section = False
    for line in lines:
        line = line.strip()

        if line.startswith('topics:'):
            in_topics_section = True
            continue

        if in_topics_section:
            # Look for topic lines that start with topic name
            if line and not line.startswith('types:') and not line.startswith('messages:'):
                # Topic lines typically look like: "  /topic_name    123 msgs    : message_type"
                parts = line.split()
                if len(parts) >= 4 and parts[0].startswith('/'):
                    topic_name = parts[0]
                    msg_count = parts[1]
                    msg_type = parts[-1] if ':' in line else 'unknown'
                    if ':' in line:
                        msg_type = line.split(':')[-1].strip()
                    topics[topic_name] = {
                        'type': msg_type,
                        'count': msg_count
                    }
            elif line.startswith('types:'):
                break

    return topics

def analyze_camera_naming():
    """Provide explanation of camera naming convention."""
    print("=" * 80)
    print("CAMERA NAMING CONVENTION ANALYSIS")
    print("=" * 80)
    print()
    print("Camera Position Codes:")
    print("  bm = Back Middle (rear center camera)")
    print("  fm = Front Middle (front center camera)")
    print("  lb = Left Back (left rear camera)")
    print("  lf = Left Front (left front camera)")
    print("  lm = Left Middle (left side camera)")
    print("  rb = Right Back (right rear camera)")
    print("  rf = Right Front (right front camera)")
    print("  rm = Right Middle (right side camera)")
    print()
    print("Camera Types:")
    print("  fisheye = Wide-angle fisheye lens cameras")
    print("  pinhole = Standard pinhole cameras")
    print("  narrow/wide = Different field-of-view configurations")
    print()
    print("This represents a typical 8-camera autonomous vehicle setup")
    print("providing 360-degree coverage around the vehicle.")
    print()

def main():
    """Main function to analyze all bag files."""
    print("ROS Bag Topic and Message Type Analyzer")
    print("=" * 80)

    # Analyze camera naming convention first
    analyze_camera_naming()

    # Find all bag files
    data_dir = "data2"
    if not os.path.exists(data_dir):
        print(f"Error: {data_dir} directory not found!")
        sys.exit(1)

    bag_files = find_bag_files(data_dir)

    if not bag_files:
        print(f"No .bag files found in {data_dir}")
        sys.exit(1)

    print(f"Found {len(bag_files)} bag files:")
    for bag_file in bag_files:
        print(f"  {bag_file}")
    print()

    # Analyze each bag file
    all_topics = defaultdict(set)

    for i, bag_file in enumerate(bag_files, 1):
        print(f"[{i}/{len(bag_files)}] Analyzing: {os.path.basename(bag_file)}")
        print("-" * 60)

        info_output = get_bag_info(bag_file)
        if info_output is None:
            print("  Failed to analyze this bag file")
            print()
            continue

        topics = parse_topics_from_info(info_output)

        if not topics:
            print("  No topics found or failed to parse")
            print()
            continue

        print(f"  Topics ({len(topics)}):")
        for topic_name, topic_info in sorted(topics.items()):
            print(f"    {topic_name:<40} {topic_info['count']:<10} msgs : {topic_info['type']}")
            all_topics[topic_info['type']].add(topic_name)

        print()

    # Summary of all message types
    print("=" * 80)
    print("SUMMARY: ALL MESSAGE TYPES FOUND")
    print("=" * 80)

    for msg_type in sorted(all_topics.keys()):
        topics_with_type = sorted(all_topics[msg_type])
        print(f"\n{msg_type}:")
        for topic in topics_with_type:
            print(f"  {topic}")

if __name__ == "__main__":
    main()