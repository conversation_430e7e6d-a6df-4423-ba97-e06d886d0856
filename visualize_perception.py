#!/usr/bin/env python3
"""
Visualize Apollo perception road structure data
Convert curb and laneline data to images
Author: <PERSON> Yu<PERSON>
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import cv2

def load_perception_data(filename):
    """Load perception message from JSON file"""
    with open(filename, 'r') as f:
        data = json.load(f)
    return data

def extract_coordinate_mapping(data):
    """Extract coordinate mapping from area definition"""
    area = data.get('area', {})
    xmin = area.get('xmin', -28)
    xmax = area.get('xmax', 119.2)
    ymin = area.get('ymin', -48)  
    ymax = area.get('ymax', 48)
    
    # Image dimensions (assuming BEV - Bird's Eye View)
    # Data is nested inside outputs_bindings
    outputs = data.get('outputs_bindings', {})
    rows = outputs.get('curb_map_rows', 736)
    cols = outputs.get('curb_map_cols', 480)
    
    print(f"Area: x[{xmin}, {xmax}], y[{ymin}, {ymax}]")
    print(f"Image size: {rows} x {cols}")
    
    return {
        'xmin': xmin, 'xmax': xmax, 'ymin': ymin, 'ymax': ymax,
        'rows': rows, 'cols': cols
    }

def pixel_to_world(pixel_ids, mapping):
    """Convert pixel IDs to world coordinates"""
    rows, cols = mapping['rows'], mapping['cols']
    xmin, xmax = mapping['xmin'], mapping['xmax']
    ymin, ymax = mapping['ymin'], mapping['ymax']
    
    # Convert 1D pixel IDs to 2D coordinates
    pixel_y = pixel_ids // cols  # row index
    pixel_x = pixel_ids % cols   # col index
    
    # Convert to world coordinates
    world_x = xmin + (pixel_x / cols) * (xmax - xmin)
    world_y = ymax - (pixel_y / rows) * (ymax - ymin)  # Y axis is flipped in image
    
    return world_x, world_y

def create_image_from_votes(vote_data, mapping, title="Road Structure"):
    """Create image from vote data"""
    rows, cols = mapping['rows'], mapping['cols']
    
    # Create empty image
    image = np.zeros((rows, cols), dtype=np.float32)
    
    # Extract vote positions and scores
    pos_ids = vote_data.get('pos_ids', [])
    scores = vote_data.get('scores', [])
    
    if len(pos_ids) == 0:
        print(f"No vote data found for {title}")
        return image
    
    print(f"{title}: {len(pos_ids)} vote positions")
    
    # Fill image with vote scores
    for i, pos_id in enumerate(pos_ids):
        if pos_id < rows * cols:
            row = pos_id // cols
            col = pos_id % cols
            score = scores[i] if i < len(scores) else 1.0
            image[row, col] = score
    
    return image

def visualize_perception_data(filename):
    """Main function to visualize perception data"""
    print("Loading perception data...")
    data = load_perception_data(filename)
    
    # Extract coordinate mapping
    mapping = extract_coordinate_mapping(data)
    
    # Get outputs_bindings data
    outputs = data.get('outputs_bindings', {})
    
    # Prepare curb data
    curb_data = {
        'pos_ids': outputs.get('curb_vote_res_pos_ids', []),
        'scores': outputs.get('curb_vote_pos_quantized_score', [])
    }
    
    # Prepare laneline data  
    laneline_data = {
        'pos_ids': outputs.get('laneline_vote_res_pos_ids', []),
        'scores': outputs.get('laneline_vote_pos_quantized_score', [])
    }
    
    # Debug: Print data availability
    print(f"Curb pos_ids count: {len(curb_data['pos_ids'])}")
    print(f"Curb scores count: {len(curb_data['scores'])}")
    print(f"Laneline pos_ids count: {len(laneline_data['pos_ids'])}")
    print(f"Laneline scores count: {len(laneline_data['scores'])}")
    
    if len(curb_data['pos_ids']) > 0:
        print(f"First few curb pos_ids: {curb_data['pos_ids'][:5]}")
        print(f"First few curb scores: {curb_data['scores'][:5]}")
    
    if len(laneline_data['pos_ids']) > 0:
        print(f"First few laneline pos_ids: {laneline_data['pos_ids'][:5]}")
        print(f"First few laneline scores: {laneline_data['scores'][:5]}")
    
    # Create images
    print("Creating curb image...")
    curb_image = create_image_from_votes(curb_data, mapping, "Curb")
    
    print("Creating laneline image...")
    laneline_image = create_image_from_votes(laneline_data, mapping, "Laneline")
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot curb image
    im1 = axes[0, 0].imshow(curb_image, cmap='hot', aspect='auto')
    axes[0, 0].set_title('Curb Detection')
    axes[0, 0].set_xlabel('X (pixels)')
    axes[0, 0].set_ylabel('Y (pixels)')
    plt.colorbar(im1, ax=axes[0, 0])
    
    # Plot laneline image
    im2 = axes[0, 1].imshow(laneline_image, cmap='hot', aspect='auto')
    axes[0, 1].set_title('Laneline Detection')
    axes[0, 1].set_xlabel('X (pixels)')
    axes[0, 1].set_ylabel('Y (pixels)')
    plt.colorbar(im2, ax=axes[0, 1])
    
    # Combined visualization
    combined = np.zeros((mapping['rows'], mapping['cols'], 3), dtype=np.float32)
    combined[:, :, 0] = curb_image / (curb_image.max() + 1e-6)  # Red for curb
    combined[:, :, 1] = laneline_image / (laneline_image.max() + 1e-6)  # Green for laneline
    
    axes[1, 0].imshow(combined)
    axes[1, 0].set_title('Combined (Red: Curb, Green: Laneline)')
    axes[1, 0].set_xlabel('X (pixels)')
    axes[1, 0].set_ylabel('Y (pixels)')
    
    # World coordinate overlay
    axes[1, 1].imshow(combined)
    axes[1, 1].set_title('With World Coordinates')
    
    # Add coordinate labels
    x_ticks = np.linspace(0, mapping['cols']-1, 5)
    y_ticks = np.linspace(0, mapping['rows']-1, 5)
    x_labels = [f"{mapping['xmin'] + (x/mapping['cols']) * (mapping['xmax']-mapping['xmin']):.1f}m" 
                for x in x_ticks]
    y_labels = [f"{mapping['ymax'] - (y/mapping['rows']) * (mapping['ymax']-mapping['ymin']):.1f}m" 
                for y in y_ticks]
    
    axes[1, 1].set_xticks(x_ticks)
    axes[1, 1].set_xticklabels(x_labels)
    axes[1, 1].set_yticks(y_ticks) 
    axes[1, 1].set_yticklabels(y_labels)
    axes[1, 1].set_xlabel('X (world coordinates)')
    axes[1, 1].set_ylabel('Y (world coordinates)')
    
    plt.tight_layout()
    plt.savefig('perception_visualization.png', dpi=300, bbox_inches='tight')
    print("Visualization saved as 'perception_visualization.png'")
    
    # Save individual images
    cv2.imwrite('curb_detection.png', (curb_image * 255).astype(np.uint8))
    cv2.imwrite('laneline_detection.png', (laneline_image * 255).astype(np.uint8))
    cv2.imwrite('combined_detection.png', (combined * 255).astype(np.uint8))
    
    print("Individual images saved:")
    print("- curb_detection.png")
    print("- laneline_detection.png") 
    print("- combined_detection.png")
    
    # Print statistics
    print(f"\nStatistics:")
    print(f"Curb votes: {len(curb_data['pos_ids'])}")
    print(f"Laneline votes: {len(laneline_data['pos_ids'])}")
    print(f"Image coverage area: {mapping['xmax']-mapping['xmin']:.1f}m x {mapping['ymax']-mapping['ymin']:.1f}m")
    print(f"Resolution: {(mapping['xmax']-mapping['xmin'])/mapping['cols']:.3f}m/pixel x {(mapping['ymax']-mapping['ymin'])/mapping['rows']:.3f}m/pixel")

if __name__ == "__main__":
    visualize_perception_data('single_perception_msg.txt') 