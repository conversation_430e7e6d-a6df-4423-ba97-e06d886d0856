#!/bin/bash

# Script to extract channel names and message types from all record files
# Author: Xiao Yubo

echo "Extracting channel information from all record files..."
echo "============================================================"

# Source the setup script
source /workspace/adc20/tools/scripts/setup.bash > /dev/null 2>&1

# Find all record files
record_files=$(find mapfree/108196_18482_WeiLai-010_yaochen_2025-06-30-10-21-10_68 -type f -name "*.record")

# Output file
output_file="channel_info_summary.txt"
> "$output_file"  # Clear the file

echo "Processing $(echo "$record_files" | wc -l) record files..."

for record_file in $record_files; do
    echo "Processing: $record_file"
    echo "========================================" >> "$output_file"
    echo "Record File: $record_file" >> "$output_file"
    echo "========================================" >> "$output_file"
    
    # Extract channel info using cyber_recorder info
    cyber_recorder info "$record_file" 2>/dev/null | grep -A 1000 "channel_info:" | grep -E "^\s+/" >> "$output_file"
    
    echo "" >> "$output_file"
done

echo "Channel information extracted to: $output_file"
echo "Summary of unique message types:"
echo "================================"

# Extract unique message types
grep -h "messages:" "$output_file" | sed 's/.*messages: //' | sort -u 